import torch
import torch.nn as nn
from .transformer import (
    Encoder, EncoderBlock, MultiHeadAttentionBlock, FeedForwardBlock,
    InputEmbeddings, LearnablePositionalEncoding, LayerNormalization
)


class SentimentClassificationHead(nn.Module):
    def __init__(self, d_model: int, num_classes: int, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(d_model, num_classes)

    def forward(self, x):
        x = self.dropout(x)
        return self.classifier(x)


class SentimentTransformer(nn.Module):
    def __init__(self, encoder: Encoder, src_embed: InputEmbeddings,
                 src_pos: LearnablePositionalEncoding, classification_head: SentimentClassificationHead):
        super().__init__()
        self.encoder = encoder
        self.src_embed = src_embed
        self.src_pos = src_pos
        self.classification_head = classification_head

    def encode(self, src, src_mask):
        src = self.src_embed(src)
        src = self.src_pos(src)
        if src_mask is not None and src_mask.dim() == 2:
            src_mask = src_mask.unsqueeze(1).unsqueeze(1)
        return self.encoder(src, src_mask)

    def classify(self, encoder_output, src_mask=None):
        if src_mask is not None:
            mask_expanded = src_mask.unsqueeze(-1).expand_as(encoder_output)
            encoder_output = encoder_output * mask_expanded.float()
            lengths = src_mask.sum(dim=1, keepdim=True).float()
            pooled = encoder_output.sum(dim=1) / lengths
        else:
            pooled = encoder_output.mean(dim=1)

        return self.classification_head(pooled)

    def forward(self, src, src_mask=None):
        encoder_output = self.encode(src, src_mask)
        return self.classify(encoder_output, src_mask)


def build_sentiment_transformer(vocab_size: int, seq_len: int, num_classes: int = 3,
                               d_model: int = 512, N: int = 6, h: int = 8,
                               dropout: float = 0.1, d_ff: int = 2048) -> SentimentTransformer:

    src_embed = InputEmbeddings(d_model, vocab_size)
    src_pos = LearnablePositionalEncoding(d_model, seq_len, dropout)

    encoder_blocks = []
    for _ in range(N):
        encoder_self_attention_block = MultiHeadAttentionBlock(d_model, h, dropout)
        feed_forward_block = FeedForwardBlock(d_model, d_ff, dropout)
        encoder_block = EncoderBlock(encoder_self_attention_block, feed_forward_block, dropout)
        encoder_blocks.append(encoder_block)

    encoder = Encoder(nn.ModuleList(encoder_blocks))
    classification_head = SentimentClassificationHead(d_model, num_classes, dropout)

    sentiment_transformer = SentimentTransformer(encoder, src_embed, src_pos, classification_head)

    for p in sentiment_transformer.parameters():
        if p.dim() > 1:
            nn.init.xavier_uniform_(p)

    return sentiment_transformer


def load_pretrained_encoder(sentiment_model, pretrained_model_path, device='cpu'):
    print(f"Loading pre-trained encoder from {pretrained_model_path}")

    if not torch.cuda.is_available():
        checkpoint = torch.load(pretrained_model_path, map_location='cpu')
    else:
        checkpoint = torch.load(pretrained_model_path, map_location=device)

    if 'model_state_dict' in checkpoint:
        pretrained_state = checkpoint['model_state_dict']
    else:
        pretrained_state = checkpoint

    sentiment_state = sentiment_model.state_dict()

    encoder_keys = [k for k in pretrained_state.keys() if k.startswith('encoder.')]
    src_embed_keys = [k for k in pretrained_state.keys() if k.startswith('src_embed.')]
    src_pos_keys = [k for k in pretrained_state.keys() if k.startswith('src_pos.')]

    loaded_keys = []
    skipped_keys = []

    for key in encoder_keys + src_embed_keys + src_pos_keys:
        if key in sentiment_state:
            pretrained_shape = pretrained_state[key].shape
            sentiment_shape = sentiment_state[key].shape

            if pretrained_shape == sentiment_shape:
                sentiment_state[key] = pretrained_state[key]
                loaded_keys.append(key)
            else:
                print(f"Skipping {key}: shape mismatch {pretrained_shape} vs {sentiment_shape}")
                skipped_keys.append(key)

    sentiment_model.load_state_dict(sentiment_state)

    print(f"Successfully loaded {len(loaded_keys)} pre-trained parameters:")
    print(f"- Encoder layers: {len([k for k in loaded_keys if k.startswith('encoder.')])}")
    print(f"- Source embeddings: {len([k for k in loaded_keys if k.startswith('src_embed.')])}")
    print(f"- Positional encodings: {len([k for k in loaded_keys if k.startswith('src_pos.')])}")

    if skipped_keys:
        print(f"Skipped {len(skipped_keys)} parameters due to shape mismatches")

    return sentiment_model
