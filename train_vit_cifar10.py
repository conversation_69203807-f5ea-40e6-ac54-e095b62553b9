#!/usr/bin/env python
# coding: utf-8

import torch
import torch.nn as nn
import torch.optim as optim
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
import time
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import os

from model.vision_transformer import build_vision_transformer

import warnings
warnings.filterwarnings('ignore')


def get_cifar10_loaders(batch_size=64, num_workers=4, data_dir='./data/cifar10'):
    train_transform = transforms.Compose([
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomRotation(degrees=10),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.4914, 0.4822, 0.4465],
                           std=[0.2023, 0.1994, 0.2010])
    ])

    test_transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.4914, 0.4822, 0.4465],
                           std=[0.2023, 0.1994, 0.2010])
    ])

    train_dataset = torchvision.datasets.CIFAR10(
        root=data_dir,
        train=True,
        download=True,
        transform=train_transform
    )

    test_dataset = torchvision.datasets.CIFAR10(
        root=data_dir,
        train=False,
        download=True,
        transform=test_transform
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )

    classes = ('plane', 'car', 'bird', 'cat', 'deer',
               'dog', 'frog', 'horse', 'ship', 'truck')

    return train_loader, test_loader, classes


def get_config(debug=True):
    if debug:
        return {
            'lr': 1e-3,
            'batch_size': 64,
            'num_epochs': 20,
            'img_size': 32,
            'patch_size': 4,
            'in_channels': 3,
            'num_classes': 10,
            'd_model': 256,
            'num_layers': 6,
            'num_heads': 8,
            'd_ff': 1024,
            'dropout': 0.1,
            'weight_decay': 1e-4,
            'save_dir': 'save/vit_models',
            'save_file': 'save/vit_models/vit_cifar10.pt'
        }
    else:
        return {
            'lr': 1e-4,
            'batch_size': 128,
            'num_epochs': 40,
            'img_size': 32,
            'patch_size': 4,
            'in_channels': 3,
            'num_classes': 10,
            'd_model': 512,
            'num_layers': 12,
            'num_heads': 8,
            'd_ff': 2048,
            'dropout': 0.1,
            'weight_decay': 1e-4,
            'save_dir': 'save/vit_models',
            'save_file': 'save/vit_models/vit_cifar10.pt'
        }


def calculate_accuracy(outputs, labels):
    _, predicted = torch.max(outputs.data, 1)
    total = labels.size(0)
    correct = (predicted == labels).sum().item()
    return correct / total


def evaluate_model(model, test_loader, criterion, device):
    model.eval()
    total_loss = 0
    total_accuracy = 0
    num_batches = 0

    with torch.no_grad():
        for images, labels in test_loader:
            images, labels = images.to(device), labels.to(device)

            outputs = model(images)
            loss = criterion(outputs, labels)
            accuracy = calculate_accuracy(outputs, labels)

            total_loss += loss.item()
            total_accuracy += accuracy
            num_batches += 1

    avg_loss = total_loss / num_batches
    avg_accuracy = total_accuracy / num_batches

    return avg_loss, avg_accuracy


def train_vision_transformer():
    DEBUG = False
    config = get_config(DEBUG)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    os.makedirs(config['save_dir'], exist_ok=True)

    print("Loading CIFAR-10 dataset...")
    train_loader, test_loader, _ = get_cifar10_loaders(
        batch_size=config['batch_size'],
        num_workers=4
    )

    print("Building Vision Transformer...")
    model = build_vision_transformer(
        img_size=config['img_size'],
        patch_size=config['patch_size'],
        in_channels=config['in_channels'],
        num_classes=config['num_classes'],
        d_model=config['d_model'],
        num_layers=config['num_layers'],
        num_heads=config['num_heads'],
        d_ff=config['d_ff'],
        dropout=config['dropout']
    ).to(device)

    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(),
                           lr=config['lr'],
                           weight_decay=config['weight_decay'])

    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=config['num_epochs'])

    train_losses = []
    train_accuracies = []
    test_losses = []
    test_accuracies = []

    best_accuracy = 0.0

    print("Starting training...")
    train_start = time.time()

    for epoch in range(config['num_epochs']):
        model.train()
        epoch_loss = 0
        epoch_accuracy = 0
        num_batches = 0

        batch_iterator = tqdm(train_loader, desc=f'Epoch {epoch+1:02d}/{config["num_epochs"]:02d}')

        for images, labels in batch_iterator:
            images, labels = images.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)

            loss.backward()
            optimizer.step()

            accuracy = calculate_accuracy(outputs, labels)

            epoch_loss += loss.item()
            epoch_accuracy += accuracy
            num_batches += 1

            batch_iterator.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{accuracy:.4f}'
            })

        avg_train_loss = epoch_loss / num_batches
        avg_train_accuracy = epoch_accuracy / num_batches

        test_loss, test_accuracy = evaluate_model(model, test_loader, criterion, device)

        scheduler.step()

        train_losses.append(avg_train_loss)
        train_accuracies.append(avg_train_accuracy)
        test_losses.append(test_loss)
        test_accuracies.append(test_accuracy)

        print(f"Epoch {epoch+1:02d}: "
              f"Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_accuracy:.4f}, "
              f"Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}")

        if test_accuracy > best_accuracy:
            best_accuracy = test_accuracy
            torch.save(model.state_dict(), config['save_file'])
            print(f"Best accuracy updated: {best_accuracy:.4f}. Model saved.")

        if (epoch + 1) % 10 == 0:
            checkpoint_path = os.path.join(config['save_dir'], f'vit_epoch_{epoch+1}.pt')
            torch.save(model.state_dict(), checkpoint_path)

    print(f"Training completed in {time.time() - train_start:.2f} seconds")
    print(f"Best test accuracy: {best_accuracy:.4f}")

    plot_training_curves(train_losses, train_accuracies, test_losses, test_accuracies, config['save_dir'])

    return model, train_losses, train_accuracies, test_losses, test_accuracies


def plot_training_curves(train_losses, train_accuracies, test_losses, test_accuracies, save_dir):
    epochs = range(1, len(train_losses) + 1)

    plt.figure(figsize=(15, 5))

    plt.subplot(1, 3, 1)
    plt.plot(epochs, train_losses, 'b-', label='Train Loss')
    plt.plot(epochs, test_losses, 'r-', label='Test Loss')
    plt.title('Training and Test Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 3, 2)
    plt.plot(epochs, train_accuracies, 'b-', label='Train Accuracy')
    plt.plot(epochs, test_accuracies, 'r-', label='Test Accuracy')
    plt.title('Training and Test Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 3, 3)
    plt.plot(epochs, train_accuracies, 'b-', label='Train Accuracy')
    plt.plot(epochs, test_accuracies, 'r-', label='Test Accuracy')
    plt.title('Model Performance')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()

    plot_path = os.path.join(save_dir, 'training_curves.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Training curves saved to {plot_path}")

    plt.show()


if __name__ == "__main__":
    model, train_losses, train_accuracies, test_losses, test_accuracies = train_vision_transformer()
