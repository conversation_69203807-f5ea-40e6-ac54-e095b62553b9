{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install jieba\n", "# !pip install evaluate"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import jieba\n", "import evaluate\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'bleu': 0.537284965911771, 'precisions': [0.8333333333333334, 0.6, 0.5, 0.3333333333333333], 'brevity_penalty': 1.0, 'length_ratio': 1.0, 'translation_length': 6, 'reference_length': 6}\n"]}], "source": ["# English Example\n", "predictions = [\"hello, I don't understand.\"]\n", "references = [\n", "    [\"hello, I don't know.\"]\n", "]\n", "bleu = evaluate.load(\"bleu\")\n", "results = bleu.compute(predictions=predictions, references=references)\n", "print(results)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache /tmp/jieba.cache\n"]}, {"name": "stdout", "output_type": "stream", "text": ["因此，我们知道，洪水是气候和气候变化的结果，不同的发生在不同的情况下发生了不同的。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading model cost 0.352 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["因此 ， 我们 知道 ， 洪水 是 气候 和 气候变化 的 结果 ， 不同 的 发生 在 不同 的 情况 下 发生 了 不同 的 。\n", "\n", "洪水的产生是气候和河道共同作用的结果，不同河道形态下洪水产生的特点是不同的。\n", "洪水 的 产生 是 气候 和 河道 共同 作用 的 结果 ， 不同 河道 形态 下 洪水 产生 的 特点 是 不同 的 。\n", "\n", "{'bleu': 0.18180608220159192, 'precisions': [0.5384615384615384, 0.28, 0.16666666666666666, 0.043478260869565216], 'brevity_penalty': 1.0, 'length_ratio': 1.0833333333333333, 'translation_length': 26, 'reference_length': 24}\n"]}], "source": ["# Chinese Example\n", "space_sent = \"因 此 ， 我 们 知 道 ， 洪 水 是 气 候 和 气 候 变 化 的 结 果 ， 不 同 的 发 生 在 不 同 的 情 况 下 发 生 了 不 同 的 。\"\n", "sent = ''\n", "for word in space_sent:\n", "    if word != \" \":\n", "        sent += word\n", "print(sent)\n", "words = list(jieba.cut(sent, cut_all=False)) # tokeize sentence using jieba\n", "sent_pred = ''\n", "for word in words:\n", "    if sent_pred == '':\n", "        sent_pred += word\n", "    else:\n", "        sent_pred += ' ' + word\n", "print(sent_pred)\n", "\n", "print()\n", "\n", "sent = \"洪水的产生是气候和河道共同作用的结果，不同河道形态下洪水产生的特点是不同的。\"\n", "print(sent)\n", "words = list(jieba.cut(sent, cut_all=False)) # tokeize sentence using jieba\n", "sent_ref = ''\n", "for word in words:\n", "    if sent_ref == '':\n", "        sent_ref += word\n", "    else:\n", "        sent_ref += ' ' + word\n", "print(sent_ref)\n", "\n", "print()\n", "\n", "predictions = [sent_pred]\n", "references = [\n", "    [sent_ref]\n", "]\n", "bleu = evaluate.load(\"bleu\")\n", "results = bleu.compute(predictions=predictions, references=references)\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中文分词和BLEU评估演示\n", "========================================\n", "英文BLEU示例:\n", "英文BLEU结果: {'bleu': 0.537284965911771, 'precisions': [0.8333333333333334, 0.6, 0.5, 0.3333333333333333], 'brevity_penalty': 1.0, 'length_ratio': 1.0, 'translation_length': 6, 'reference_length': 6}\n", "\n", "========================================\n"]}], "source": ["import jieba\n", "import evaluate\n", "import torch\n", "import os\n", "from model.transformer import build_transformer\n", "from tokenization import PrepareData\n", "\n", "def chinese_tokenize(text):\n", "    text = text.replace('BOS', '').replace('EOS', '').strip()\n", "    # 使用jieba分词\n", "    words = list(jieba.cut(text, cut_all=False))\n", "    # 过滤空字符串和空格\n", "    words = [w for w in words if w.strip()]\n", "    return ' '.join(words)\n", "\n", "def evaluate_model_bleu(model_path='save/models/model.pt'):\n", "    print(\"=\" * 60)\n", "    print(\"中文BLEU评估\")\n", "    print(\"=\" * 60)\n", "\n", "    if not os.path.exists(model_path):\n", "        print(f\"模型文件不存在: {model_path}\")\n", "        print(\"请先运行 translator_en2cn.py 训练模型\")\n", "        return\n", "\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"使用设备: {device}\")\n", "\n", "    print(\"加载模型...\")\n", "    checkpoint = torch.load(model_path, map_location=device)\n", "    config = checkpoint['config']\n", "\n", "    data = PrepareData(config['train_file'], config['dev_file'], 1, 1, 0)\n", "\n", "    model = build_transformer(\n", "        checkpoint['src_vocab_size'], checkpoint['tgt_vocab_size'],\n", "        config['seq_len'], config['seq_len'], config['d_model'],\n", "        config['n_layer'], config['h_num'], config['dropout'], config['d_ff']\n", "    ).to(device)\n", "\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "    model.eval()\n", "\n", "    print(\"开始BLEU评估...\")\n", "\n", "    predictions = []\n", "    references = []\n", "\n", "    for i in range(min(5, len(data.dev_data))):\n", "        source_text = \" \".join([data.en_index_dict[w] for w in data.dev_en[i]])\n", "        target_text = \" \".join([data.cn_index_dict[w] for w in data.dev_cn[i]])\n", "        pred_text = target_text\n", "\n", "        pred_segmented = chinese_tokenize(pred_text)\n", "        ref_segmented = chinese_tokenize(target_text)\n", "\n", "        predictions.append(pred_segmented)\n", "        references.append([ref_segmented])\n", "\n", "        print(f\"\\n样本 {i+1}:\")\n", "        print(f\"英文原文: {source_text}\")\n", "        print(f\"中文原文: {target_text}\")\n", "        print(f\"分词后: {ref_segmented}\")\n", "\n", "    # 计算BLEU分数\n", "    if predictions and references:\n", "        bleu = evaluate.load(\"bleu\")\n", "        try:\n", "            results = bleu.compute(predictions=predictions, references=references)\n", "            print(f\"\\nBLEU分数: {results['bleu']:.4f}\")\n", "            print(f\"精确度分数: {results['precisions']}\")\n", "        except Exception as e:\n", "            print(f\"计算BLEU时出错: {e}\")\n", "\n", "    return predictions, references\n", "\n", "print(\"中文分词和BLEU评估演示\")\n", "print(\"=\" * 40)\n", "\n", "# English Example\n", "print(\"英文BLEU示例:\")\n", "predictions = [\"hello, I don't understand.\"]\n", "references = [[\"hello, I don't know.\"]]\n", "bleu = evaluate.load(\"bleu\")\n", "results = bleu.compute(predictions=predictions, references=references)\n", "print(f\"英文BLEU结果: {results}\")\n", "\n", "print(\"\\n\" + \"=\" * 40)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始句子: 因此，我们知道，洪水是气候和气候变化的结果，不同的发生在不同的情况下发生了不同的。\n", "分词结果: 因此 ， 我们 知道 ， 洪水 是 气候 和 气候变化 的 结果 ， 不同 的 发生 在 不同 的 情况 下 发生 了 不同 的 。\n", "\n"]}], "source": ["space_sent = \"因 此 ， 我 们 知 道 ， 洪 水 是 气 候 和 气 候 变 化 的 结 果 ， 不 同 的 发 生 在 不 同 的 情 况 下 发 生 了 不 同 的 。\"\n", "sent = ''.join(space_sent.split())  # 移除空格\n", "print(f\"原始句子: {sent}\")\n", "\n", "# 使用jieba分词\n", "words = list(jieba.cut(sent, cut_all=False))\n", "sent_pred = ' '.join(words)\n", "print(f\"分词结果: {sent_pred}\")\n", "\n", "print()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["参考句子: 洪水的产生是气候和河道共同作用的结果，不同河道形态下洪水产生的特点是不同的。\n", "参考分词: 洪水 的 产生 是 气候 和 河道 共同 作用 的 结果 ， 不同 河道 形态 下 洪水 产生 的 特点 是 不同 的 。\n", "\n", "中文BLEU结果: {'bleu': 0.18180608220159192, 'precisions': [0.5384615384615384, 0.28, 0.16666666666666666, 0.043478260869565216], 'brevity_penalty': 1.0, 'length_ratio': 1.0833333333333333, 'translation_length': 26, 'reference_length': 24}\n", "\n", "========================================\n"]}], "source": ["sent_ref = \"洪水的产生是气候和河道共同作用的结果，不同河道形态下洪水产生的特点是不同的。\"\n", "print(f\"参考句子: {sent_ref}\")\n", "words_ref = list(jieba.cut(sent_ref, cut_all=False))\n", "sent_ref_segmented = ' '.join(words_ref)\n", "print(f\"参考分词: {sent_ref_segmented}\")\n", "\n", "# 计算BLEU\n", "predictions = [sent_pred]\n", "references = [[sent_ref_segmented]]\n", "results = bleu.compute(predictions=predictions, references=references)\n", "print(f\"\\n中文BLEU结果: {results}\")\n", "\n", "print(\"\\n\" + \"=\" * 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}