# 实验指南：消融研究和超参数调优

## 📋 实验概述

基于您已实现的功能，现在可以进行系统的消融实验来完善报告。以下是具体的实验步骤和配置。

## 🔧 已实现的功能

### ✅ 位置编码切换功能
- **Learnable**: `pos_encoding_type: 'learnable'` - 可学习的位置嵌入
- **Fixed**: `pos_encoding_type: 'fixed'` - 固定正弦位置编码  
- **None**: `pos_encoding_type: 'none'` - 无位置编码

### ✅ Cosine Annealing with Warm-up 学习率调度器
- **Warm-up阶段**: 线性增长到最大学习率
- **Cosine衰减阶段**: 余弦曲线衰减到最小学习率
- **自动可视化**: 学习率曲线图

## 🧪 实验配置

### 1. 位置编码消融实验

修改 `translator_en2cn.py` 中的配置：

```python
# 实验1: Learnable Positional Encoding (基线)
config['pos_encoding_type'] = 'learnable'

# 实验2: Fixed Sinusoidal Positional Encoding  
config['pos_encoding_type'] = 'fixed'

# 实验3: No Positional Encoding
config['pos_encoding_type'] = 'none'
```

### 2. 模型架构消融实验

```python
# 基线配置
baseline_config = {
    'n_layer': 6, 'd_model': 256, 'd_ff': 1024, 'h_num': 8,
    'max_lr': 5e-4, 'warmup_epochs': 3, 'num_epochs': 20
}

# 小模型配置
small_config = {
    'n_layer': 3, 'd_model': 128, 'd_ff': 256, 'h_num': 8,
    'max_lr': 1e-3, 'warmup_epochs': 2, 'num_epochs': 15
}

# 减少注意力头
reduced_heads_config = {
    'n_layer': 6, 'd_model': 256, 'd_ff': 1024, 'h_num': 4,
    'max_lr': 5e-4, 'warmup_epochs': 3, 'num_epochs': 20
}

# 更深网络
deeper_config = {
    'n_layer': 8, 'd_model': 256, 'd_ff': 1024, 'h_num': 8,
    'max_lr': 3e-4, 'warmup_epochs': 4, 'num_epochs': 20
}
```

### 3. 学习率调度器消融实验

```python
# 保守策略
conservative_lr = {
    'lr': 1e-5, 'max_lr': 5e-4, 'min_lr': 1e-6, 'warmup_epochs': 3
}

# 激进策略  
aggressive_lr = {
    'lr': 1e-4, 'max_lr': 1e-3, 'min_lr': 1e-6, 'warmup_epochs': 2
}

# 长预热策略
long_warmup_lr = {
    'lr': 1e-5, 'max_lr': 5e-4, 'min_lr': 1e-6, 'warmup_epochs': 5
}
```

## 📊 实验执行步骤

### 步骤1: 运行基线实验
```bash
# 修改 translator_en2cn.py 中的配置为基线设置
python translator_en2cn.py
```

### 步骤2: 记录结果
每次实验后记录：
- 最终BLEU分数
- 训练时间
- 最终验证损失
- 学习率曲线图 (`training_curves_with_lr.png`)

### 步骤3: 系统性实验
按以下顺序进行实验：

1. **位置编码对比** (固定其他参数)
   - Learnable PE
   - Fixed PE  
   - No PE

2. **模型架构对比** (固定learnable PE)
   - 基线模型
   - 小模型
   - 减少注意力头
   - 更深网络

3. **学习率策略对比** (固定基线架构)
   - 保守学习率
   - 激进学习率
   - 长预热

## 📈 结果分析框架

### 性能指标
- **BLEU分数**: 翻译质量主要指标
- **训练损失收敛**: 训练稳定性
- **验证损失**: 泛化能力
- **训练时间**: 计算效率
- **参数数量**: 模型复杂度

### 预期结果
- **Learnable PE** > **Fixed PE** > **No PE**
- **更深/更宽模型** 通常有更好的BLEU，但训练时间更长
- **Warm-up + Cosine** > **固定学习率**

## 📝 报告更新

### 更新 `updated_report_template.md`

1. **填入实际BLEU分数**:
   ```markdown
   | Positional Encoding | Final BLEU | Notes |
   |-------------------|------------|-------|
   | Learnable | 0.XXX | 实际测试结果 |
   | Fixed | 0.XXX | 实际测试结果 |
   | None | 0.XXX | 实际测试结果 |
   ```

2. **添加学习率曲线图**:
   ```markdown
   ![Learning Rate Schedule](training_curves_with_lr.png)
   ```

3. **更新架构对比表**:
   ```markdown
   | Configuration | Final BLEU | Training Time | Parameters |
   |--------------|------------|---------------|------------|
   | Baseline | 0.XXX | XXX min | XX.XM |
   | Small Model | 0.XXX | XXX min | XX.XM |
   ```

## 🚀 快速验证

运行快速测试确保所有配置正常：
```bash
python quick_ablation_test.py
```

## 💡 实验建议

1. **先运行debug配置** (较少epochs) 验证设置正确
2. **保存每个实验的模型** 以便后续分析
3. **记录详细日志** 包括配置和结果
4. **可视化对比** 使用图表展示不同配置的性能

## 📋 实验检查清单

- [ ] 基线实验 (Learnable PE + 标准架构)
- [ ] Fixed PE实验
- [ ] No PE实验  
- [ ] 小模型实验
- [ ] 减少注意力头实验
- [ ] 更深网络实验
- [ ] 不同学习率策略实验
- [ ] 结果记录和分析
- [ ] 报告更新

完成这些实验后，您将有完整的消融研究数据来支撑报告的结论！
